---
marp: true
theme: default
style: |
  @import url('./glassmorphism-marp-theme.css');
size: 16:9
paginate: true
---

<div class="version-tag">SOURCE BTN<br>V.02</div>

<div class="center-content">

# 毛玻璃拟态设计

## 现代化演示模板

<div class="button-group">
  <div class="glass-button-circle">↑</div>
  <div class="glass-button">Upload</div>
</div>

</div>

---

## 设计特色

<div class="glass-card fade-in">

### 🎨 视觉效果
- **毛玻璃质感**：半透明磨砂效果
- **柔和渐变**：优雅的色彩过渡
- **微妙阴影**：立体层次感

</div>

<div class="glass-card fade-in">

### ⚡ 交互体验
- **流畅动画**：自然的过渡效果
- **响应式设计**：适配各种屏幕
- **现代UI元素**：圆角和胶囊按钮

</div>

---

## 双栏布局展示

<div class="two-columns">

<div class="column">

### 左侧内容

这是一个展示双栏布局的示例。毛玻璃效果让内容看起来更加现代和优雅。

- 清晰的层次结构
- 优秀的可读性
- 美观的视觉效果

</div>

<div class="column">

### 右侧内容

```css
.glass-card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
}
```

代码块也采用了毛玻璃风格设计。

</div>

</div>

---

<div class="center-content">

## 感谢观看

<div class="glass-card" style="max-width: 600px;">

这个模板完美复现了您提供的设计风格，包含：

- 毛玻璃拟态效果
- 现代化按钮设计  
- 优雅的渐变背景
- 响应式布局支持

</div>

<div class="button-group">
  <div class="glass-button">开始使用</div>
  <div class="glass-button">了解更多</div>
</div>

</div>

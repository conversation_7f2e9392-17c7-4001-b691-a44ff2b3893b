/* ===== Glassmorphism Marp Theme ===== */

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

:root {
  --primary-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --glass-bg: rgba(255, 255, 255, 0.1);
  --glass-border: rgba(255, 255, 255, 0.2);
  --text-primary: #ffffff;
  --text-secondary: rgba(255, 255, 255, 0.8);
  --text-muted: rgba(255, 255, 255, 0.6);
  --shadow-light: 0 8px 32px rgba(31, 38, 135, 0.37);
  --shadow-medium: 0 15px 35px rgba(31, 38, 135, 0.5);
  --border-radius: 20px;
  --border-radius-small: 12px;
}

/* ===== Base Styles ===== */
section {
  background: var(--primary-bg);
  color: var(--text-primary);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  font-weight: 400;
  line-height: 1.6;
  padding: 60px;
  position: relative;
  overflow: hidden;
}

section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
  pointer-events: none;
}

/* ===== Typography ===== */
h1 {
  font-size: 3.5rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  text-align: center;
  background: linear-gradient(135deg, #ffffff 0%, rgba(255, 255, 255, 0.8) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

h2 {
  font-size: 2.5rem;
  font-weight: 600;
  margin-bottom: 1.2rem;
  color: var(--text-primary);
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
}

h3 {
  font-size: 1.8rem;
  font-weight: 500;
  margin-bottom: 1rem;
  color: var(--text-secondary);
}

p {
  font-size: 1.2rem;
  margin-bottom: 1rem;
  color: var(--text-secondary);
  line-height: 1.7;
}

/* ===== Glass Cards ===== */
.glass-card {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: var(--border-radius);
  padding: 2rem;
  margin: 1.5rem 0;
  box-shadow: var(--shadow-light);
  position: relative;
  z-index: 1;
}

/* ===== Buttons ===== */
.glass-button {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: 50px;
  padding: 1rem 2rem;
  color: var(--text-primary);
  font-weight: 500;
  text-decoration: none;
  display: inline-block;
  margin: 0.5rem;
  box-shadow: var(--shadow-light);
  transition: all 0.3s ease;
}

.glass-button:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.glass-button-circle {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: var(--text-primary);
  box-shadow: var(--shadow-light);
  margin: 1rem auto;
  transition: all 0.3s ease;
}

.glass-button-circle:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-medium);
}

/* ===== Lists ===== */
ul, ol {
  margin: 1.5rem 0;
  padding-left: 2rem;
}

li {
  margin-bottom: 0.8rem;
  color: var(--text-secondary);
  font-size: 1.1rem;
}

li::marker {
  color: var(--text-primary);
}

/* ===== Code Blocks ===== */
code {
  background: rgba(0, 0, 0, 0.3);
  padding: 0.3rem 0.6rem;
  border-radius: var(--border-radius-small);
  font-family: 'Monaco', 'Menlo', monospace;
  color: var(--text-primary);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

pre {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  margin: 1.5rem 0;
  overflow-x: auto;
  box-shadow: var(--shadow-light);
}

/* ===== Special Elements ===== */
.version-tag {
  position: absolute;
  top: 40px;
  left: 60px;
  font-size: 0.9rem;
  color: var(--text-muted);
  font-weight: 300;
  letter-spacing: 0.1em;
  text-transform: uppercase;
}

.center-content {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
  text-align: center;
}

.button-group {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  margin: 2rem 0;
}

/* ===== Responsive Design ===== */
@media (max-width: 768px) {
  section {
    padding: 40px;
  }
  
  h1 {
    font-size: 2.5rem;
  }
  
  h2 {
    font-size: 2rem;
  }
  
  .glass-card {
    padding: 1.5rem;
  }
}

/* ===== Animation ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeInUp 0.8s ease-out;
}

/* ===== Two Column Layout ===== */
.two-columns {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 3rem;
  align-items: start;
}

.column {
  background: var(--glass-bg);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid var(--glass-border);
  border-radius: var(--border-radius);
  padding: 2rem;
  box-shadow: var(--shadow-light);
}

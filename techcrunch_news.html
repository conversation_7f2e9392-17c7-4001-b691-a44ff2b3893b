<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TechCrunch 热点新闻 - 毛玻璃拟态设计</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans SC', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            position: relative;
            overflow-x: hidden;
        }

        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: 
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);
            z-index: -1;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            position: relative;
            z-index: 1;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 30px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 
                0 8px 32px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .header h1 {
            color: white;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .header p {
            color: rgba(255, 255, 255, 0.8);
            font-size: 1.1rem;
            font-weight: 300;
        }

        .news-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .news-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(15px);
            border-radius: 20px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 
                0 8px 32px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .news-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #4285f4, #ea4335, #fbbc05, #34a853);
            border-radius: 20px 20px 0 0;
        }

        .news-card:hover {
            transform: translateY(-5px);
            box-shadow: 
                0 15px 40px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
        }

        .news-category {
            display: inline-block;
            background: rgba(66, 133, 244, 0.8);
            color: white;
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
            margin-bottom: 15px;
            backdrop-filter: blur(10px);
        }

        .news-title {
            color: white;
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 12px;
            line-height: 1.4;
            text-shadow: 0 1px 3px rgba(0, 0, 0, 0.3);
        }

        .news-summary {
            color: rgba(255, 255, 255, 0.85);
            font-size: 0.95rem;
            line-height: 1.6;
            margin-bottom: 15px;
        }

        .news-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.85rem;
        }

        .news-date {
            background: rgba(255, 255, 255, 0.1);
            padding: 4px 8px;
            border-radius: 8px;
            backdrop-filter: blur(5px);
        }

        .read-more {
            color: #4285f4;
            text-decoration: none;
            font-weight: 500;
            transition: color 0.3s ease;
        }

        .read-more:hover {
            color: #ffffff;
        }

        .footer {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 
                0 8px 32px rgba(0, 0, 0, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .footer p {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }
            
            .news-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .news-card {
                padding: 20px;
            }
        }

        .floating-elements {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: -1;
        }

        .floating-circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            animation: float 6s ease-in-out infinite;
        }

        .floating-circle:nth-child(1) {
            width: 80px;
            height: 80px;
            top: 10%;
            left: 10%;
            animation-delay: 0s;
        }

        .floating-circle:nth-child(2) {
            width: 120px;
            height: 120px;
            top: 60%;
            right: 10%;
            animation-delay: 2s;
        }

        .floating-circle:nth-child(3) {
            width: 60px;
            height: 60px;
            bottom: 20%;
            left: 20%;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
            }
            50% {
                transform: translateY(-20px) rotate(180deg);
            }
        }
    </style>
</head>
<body>
    <div class="floating-elements">
        <div class="floating-circle"></div>
        <div class="floating-circle"></div>
        <div class="floating-circle"></div>
    </div>

    <div class="container">
        <header class="header">
            <h1>🚀 TechCrunch 热点新闻</h1>
            <p>2025年6月9日 · 过去48小时最新科技资讯 · 毛玻璃拟态设计</p>
        </header>

        <div class="news-grid">
            <article class="news-card">
                <span class="news-category">🍎 苹果WWDC</span>
                <h2 class="news-title">WWDC 2025今日开幕：iOS 19等重大更新即将发布</h2>
                <p class="news-summary">
                    苹果全球开发者大会WWDC 2025于6月9-13日举行，预计将发布iOS 19、macOS等重大软件更新。
                    今年的焦点将是AI功能的进一步整合和新的开发者工具。
                </p>
                <div class="news-meta">
                    <span class="news-date">6月9日</span>
                    <a href="https://techcrunch.com/2025/06/08/wwdc-2025-what-to-expect-from-this-years-conference/" target="_blank" class="read-more">阅读更多 →</a>
                </div>
            </article>

            <article class="news-card">
                <span class="news-category">💰 保险科技</span>
                <h2 class="news-title">Bolttech完成1.47亿美元C轮融资，估值21亿美元</h2>
                <p class="news-summary">
                    新加坡嵌入式保险创业公司Bolttech完成1.47亿美元C轮融资，估值达21亿美元。
                    公司连接700多个分销合作伙伴与230多家保险公司，年化保费总额达650亿美元。
                </p>
                <div class="news-meta">
                    <span class="news-date">6月4日</span>
                    <a href="https://techcrunch.com/2025/06/04/singapore-based-insurtech-bolttech-closes-147m-series-c-at-a-2-1b-valuation/" target="_blank" class="read-more">阅读更多 →</a>
                </div>
            </article>

            <article class="news-card">
                <span class="news-category">🔒 网络安全</span>
                <h2 class="news-title">特朗普政府瞄准拜登和奥巴马时代网络安全规则</h2>
                <p class="news-summary">
                    特朗普政府开始审查和修改前任政府制定的网络安全政策和规则，
                    这可能对美国的网络安全防护体系产生重大影响。
                </p>
                <div class="news-meta">
                    <span class="news-date">6月7日</span>
                    <a href="https://techcrunch.com/2025/06/07/trump-administration-takes-aim-at-biden-and-obama-cybersecurity-rules/" target="_blank" class="read-more">阅读更多 →</a>
                </div>
            </article>

            <article class="news-card">
                <span class="news-category">🤖 AI投资</span>
                <h2 class="news-title">马斯克与特朗普分歧是否影响xAI的50亿美元债务交易？</h2>
                <p class="news-summary">
                    埃隆·马斯克的AI公司xAI正在寻求50亿美元的债务融资，
                    但马斯克与特朗普之间的政治分歧可能对这笔交易产生影响。
                </p>
                <div class="news-meta">
                    <span class="news-date">6月7日</span>
                    <a href="https://techcrunch.com/2025/06/07/will-musk-vs-trump-affect-xais-5-billion-debt-deal/" target="_blank" class="read-more">阅读更多 →</a>
                </div>
            </article>

            <article class="news-card">
                <span class="news-category">💻 科技人物</span>
                <h2 class="news-title">苹果先驱工程师比尔·阿特金森去世，享年74岁</h2>
                <p class="news-summary">
                    苹果公司早期关键工程师比尔·阿特金森去世，他曾参与开发Lisa和早期Macintosh计算机，
                    并创造了MacPaint等经典软件应用。
                </p>
                <div class="news-meta">
                    <span class="news-date">6月8日</span>
                    <a href="https://techcrunch.com/2025/06/08/pioneering-apple-engineer-bill-atkinson-dies-at-74/" target="_blank" class="read-more">阅读更多 →</a>
                </div>
            </article>

            <article class="news-card">
                <span class="news-category">🏪 电商支付</span>
                <h2 class="news-title">PayPal在应用内添加酒店预订功能，由Selfbook提供支持</h2>
                <p class="news-summary">
                    PayPal宣布在其移动应用中集成酒店预订服务，该功能由旅游科技公司Selfbook提供技术支持，
                    进一步扩展其金融服务生态系统。
                </p>
                <div class="news-meta">
                    <span class="news-date">6月9日</span>
                    <a href="https://techcrunch.com/2025/06/09/paypal-is-adding-hotel-booking-with-in-its-app-powered-by-selfbook/" target="_blank" class="read-more">阅读更多 →</a>
                </div>
            </article>
        </div>

        <footer class="footer">
            <p>📱 数据来源：TechCrunch | 🎨 毛玻璃拟态设计 | ⚡ 2025年6月9日更新</p>
        </footer>
    </div>
</body>
</html>
